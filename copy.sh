#!/bin/bash

# === CONFIGURATION ===
BASE_PATH="Data/Prod_data_dataset/SS-00579"
BASE_URL="https://evidentai.blob.core.windows.net/tpi/$BASE_PATH"
SAS_TOKEN="?sv=2023-01-03&se=2025-08-24T12%3A50%3A57Z&sr=c&sp=rl&sig=fIDJkquvkUHsL3t6zgIIJe9mj7O2xoUY7tXBz2BR1aQ%3D"
DEST_FOLDER="./Data/prod_data_dataset"
BLOB_LIST="/tmp/blob_list.txt"
VOLUME_LIST="/tmp/volume_data_list.txt"

# === 1. LISTER LES BLOBS ===
echo "📦 Listing blobs under $BASE_PATH ..."
./azcopy list "${BASE_URL}${SAS_TOKEN}" --output-type=text > "$BLOB_LIST"

# === 2. FILTRER /volume_data/ ET ENLEVER LE TEXTE INUTILE ===
echo "🔍 Filtering for 'volume_data' blobs..."
grep -i "/volume_data/" "$BLOB_LIST" | awk -F';' '{print $1}' > "$VOLUME_LIST"

echo "▶️ Matching blobs found:"
cat "$VOLUME_LIST"

# === 3. TÉLÉCHARGEMENT DES FICHIERS ===
echo "⬇️ Downloading volume_data files to: $DEST_FOLDER"
AZCOPY_CRED_TYPE=Anonymous ./azcopy copy "${BASE_URL}${SAS_TOKEN}" "$DEST_FOLDER" \
  --from-to=BlobLocal \
  --list-of-files="$VOLUME_LIST" \
  --overwrite=true \
  --log-level=INFO

echo "✅ Done."
